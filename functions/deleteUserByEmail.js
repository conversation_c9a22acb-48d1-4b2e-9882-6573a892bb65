const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}

/**
 * Cloud Function to delete a user from Firebase Authentication by email
 * This is needed for admin operations since client-side code cannot delete other users
 */
exports.deleteUserByEmail = functions.https.onCall(async (data, context) => {
    try {
        // Verify that the request is coming from an authenticated admin
        // For now, we'll allow any authenticated user to call this
        // In production, you should add proper admin role verification
        if (!context.auth) {
            throw new functions.https.HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.'
            );
        }

        const { email } = data;

        if (!email) {
            throw new functions.https.HttpsError(
                'invalid-argument',
                'Email is required.'
            );
        }

        console.log(`Attempting to delete user with email: ${email}`);

        try {
            // Get user by email
            const userRecord = await admin.auth().getUserByEmail(email);
            console.log(`Found user with UID: ${userRecord.uid}`);

            // Delete the user
            await admin.auth().deleteUser(userRecord.uid);
            console.log(`Successfully deleted user with email: ${email} and UID: ${userRecord.uid}`);

            return {
                success: true,
                message: `User with email ${email} deleted successfully`,
                deletedUid: userRecord.uid
            };

        } catch (authError) {
            if (authError.code === 'auth/user-not-found') {
                console.log(`User with email ${email} not found in Firebase Auth`);
                return {
                    success: true,
                    message: `User with email ${email} was not found in Firebase Auth (may have been already deleted)`,
                    deletedUid: null
                };
            } else {
                console.error(`Error deleting user from Firebase Auth:`, authError);
                throw new functions.https.HttpsError(
                    'internal',
                    `Failed to delete user from Firebase Auth: ${authError.message}`
                );
            }
        }

    } catch (error) {
        console.error('Error in deleteUserByEmail function:', error);
        
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        
        throw new functions.https.HttpsError(
            'internal',
            `An unexpected error occurred: ${error.message}`
        );
    }
});
