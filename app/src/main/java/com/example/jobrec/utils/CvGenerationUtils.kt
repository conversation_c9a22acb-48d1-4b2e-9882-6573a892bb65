package com.example.jobrec.utils

import android.util.Base64
import android.util.Log
import com.example.jobrec.User
import com.example.jobrec.services.GeminiCvGenerationService
import com.google.firebase.Timestamp
import com.google.firebase.firestore.FirebaseFirestore
import com.itextpdf.html2pdf.HtmlConverter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.IOException

class CvGenerationUtils {

    companion object {
        private const val TAG = "CvGenerationUtils"
        private val geminiService = GeminiCvGenerationService()
        private val db = FirebaseFirestore.getInstance()

        /**
         * Main function to generate and store CV from user profile
         * This is the entry point called from JobDetailsActivity
         */
        suspend fun generateAndStoreCvFromProfile(user: User, userId: String): String = withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting CV generation for user: ${user.email}")

                // Step 1: Generate HTML CV using Gemini AI
                val htmlContent = geminiService.generateFormattedCv(user)
                Log.d(TAG, "HTML CV generated successfully")

                // Step 2: Convert HTML to PDF
                val pdfBytes = convertHtmlToPdf(htmlContent)
                Log.d(TAG, "PDF conversion completed, size: ${pdfBytes.size} bytes")

                // Step 3: Encode PDF to Base64
                val base64String = Base64.encodeToString(pdfBytes, Base64.DEFAULT)
                Log.d(TAG, "PDF encoded to base64, size: ${base64String.length} characters")

                // Step 4: Store in Firestore
                val documentId = storeCvInFirestore(userId, base64String, htmlContent)
                Log.d(TAG, "CV stored in Firestore with ID: $documentId")

                return@withContext documentId

            } catch (e: Exception) {
                Log.e(TAG, "Error in CV generation process", e)
                throw e
            }
        }

        /**
         * Convert HTML content to PDF using iText
         */
        private fun convertHtmlToPdf(htmlContent: String): ByteArray {
            return try {
                val outputStream = ByteArrayOutputStream()

                // Configure HTML to PDF conversion
                val converterProperties = com.itextpdf.html2pdf.ConverterProperties()

                // Set base URI for relative resources (if needed)
                // converterProperties.setBaseUri("file:///android_asset/")

                // Convert HTML to PDF
                HtmlConverter.convertToPdf(htmlContent, outputStream, converterProperties)

                val pdfBytes = outputStream.toByteArray()
                outputStream.close()

                Log.d(TAG, "HTML to PDF conversion successful")
                pdfBytes

            } catch (e: Exception) {
                Log.e(TAG, "Error converting HTML to PDF", e)
                throw IOException("Failed to convert HTML to PDF: ${e.message}", e)
            }
        }

        /**
         * Store CV in Firestore with metadata
         */
        private suspend fun storeCvInFirestore(
            userId: String,
            base64String: String,
            htmlContent: String
        ): String {
            return try {
                val cvRef = db.collection("cvs").document()
                val cvData = hashMapOf(
                    "userId" to userId,
                    "cvBase64" to base64String,
                    "htmlContent" to htmlContent,
                    "fileName" to "CV_${userId}_${System.currentTimeMillis()}.pdf",
                    "uploadedAt" to Timestamp.now(),
                    "fileSize" to base64String.length,
                    "generatedBy" to "AI",
                    "version" to "1.0"
                )

                cvRef.set(cvData).await()
                Log.d(TAG, "CV successfully stored in Firestore")

                cvRef.id

            } catch (e: Exception) {
                Log.e(TAG, "Error storing CV in Firestore", e)
                throw e
            }
        }

        /**
         * Generate a simple text-based CV as fallback
         */
        fun generateSimpleCvContent(user: User): String {
            return buildString {
                append("${user.name} ${user.surname}\n")
                append("${user.email}\n")
                append("${user.phoneNumber}\n")
                append("${user.city}, ${user.province}\n\n")

                if (user.summary.isNotEmpty()) {
                    append("PROFESSIONAL SUMMARY\n")
                    append("${user.summary}\n\n")
                }

                if (user.skills.isNotEmpty()) {
                    append("SKILLS\n")
                    append("${user.skills.joinToString(", ")}\n\n")
                }

                if (user.experience.isNotEmpty()) {
                    append("PROFESSIONAL EXPERIENCE\n")
                    user.experience.forEach { exp ->
                        append("${exp.position} at ${exp.company}\n")
                        append("${exp.startDate} - ${exp.endDate}\n")
                        if (exp.description.isNotEmpty()) {
                            append("${exp.description}\n")
                        }
                        append("\n")
                    }
                }

                if (user.education.isNotEmpty()) {
                    append("EDUCATION\n")
                    user.education.forEach { edu ->
                        append("${edu.degree} in ${edu.fieldOfStudy}\n")
                        append("${edu.institution}, ${edu.endDate}\n\n")
                    }
                }

                if (user.languages.isNotEmpty()) {
                    append("LANGUAGES\n")
                    append("${user.languages.joinToString(", ") { "${it.name} (${it.proficiency})" }}\n\n")
                }

                if (user.achievements.isNotEmpty()) {
                    append("ACHIEVEMENTS\n")
                    append("${user.achievements}\n\n")
                }

                if (user.certificates.isNotEmpty()) {
                    append("CERTIFICATIONS\n")
                    user.certificates.forEach { cert ->
                        append("${cert["name"]} from ${cert["issuer"]} (${cert["date"]})\n")
                    }
                    append("\n")
                }

                // Add links if available
                val links = mutableListOf<String>()
                if (user.linkedin.isNotEmpty()) links.add("LinkedIn: ${user.linkedin}")
                if (user.github.isNotEmpty()) links.add("GitHub: ${user.github}")
                if (user.portfolio.isNotEmpty()) links.add("Portfolio: ${user.portfolio}")

                if (links.isNotEmpty()) {
                    append("LINKS\n")
                    append("${links.joinToString("\n")}\n")
                }
            }
        }

        /**
         * Validate user profile completeness for CV generation
         */
        fun validateUserProfile(user: User): List<String> {
            val missingFields = mutableListOf<String>()

            if (user.name.isEmpty()) missingFields.add("Name")
            if (user.surname.isEmpty()) missingFields.add("Surname")
            if (user.email.isEmpty()) missingFields.add("Email")
            if (user.phoneNumber.isEmpty()) missingFields.add("Phone Number")
            if (user.city.isEmpty()) missingFields.add("City")
            if (user.province.isEmpty()) missingFields.add("Province")

            return missingFields
        }

        /**
         * Check if user has sufficient profile data for a quality CV
         */
        fun hasMinimumProfileData(user: User): Boolean {
            val requiredFields = listOf(
                user.name.isNotEmpty(),
                user.surname.isNotEmpty(),
                user.email.isNotEmpty(),
                user.phoneNumber.isNotEmpty()
            )

            val optionalButImportant = listOf(
                user.summary.isNotEmpty(),
                user.skills.isNotEmpty(),
                user.experience.isNotEmpty() || user.education.isNotEmpty()
            )

            return requiredFields.all { it } && optionalButImportant.any { it }
        }
    }
}
