package com.example.jobrec.services

import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.generationConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log
import com.example.jobrec.BuildConfig
import com.example.jobrec.User

class GeminiCvGenerationService {

    companion object {
        private const val TAG = "GeminiCvGeneration"
        private val API_KEY = BuildConfig.GEMINI_API_KEY
    }

    private val generativeModel = GenerativeModel(
        modelName = "gemini-pro",
        apiKey = API_KEY,
        generationConfig = generationConfig {
            temperature = 0.3f
            topK = 5
            topP = 0.8f
            maxOutputTokens = 4000
        }
    )

    suspend fun generateFormattedCv(user: User): String = withContext(Dispatchers.IO) {
        try {
            val prompt = buildCvPrompt(user)
            Log.d(TAG, "Sending CV generation prompt to <PERSON>")

            val response = generativeModel.generateContent(prompt)
            val htmlContent = response.text ?: generateFallbackCv(user)

            Log.d(TAG, "Gemini CV generation successful")
            return@withContext cleanHtmlContent(htmlContent)

        } catch (e: Exception) {
            Log.e(TAG, "Error generating CV with Gemini", e)
            return@withContext generateFallbackCv(user)
        }
    }

    private fun buildCvPrompt(user: User): String {
        return """
            Generate a professional HTML CV/Resume for the following candidate. The CV should be modern, clean, and ATS-friendly.

            CANDIDATE INFORMATION:
            Name: ${user.name} ${user.surname}
            Email: ${user.email}
            Phone: ${user.phoneNumber}
            Location: ${user.city}, ${user.province}
            Field: ${user.field}
            Sub-field: ${user.subField}
            Years of Experience: ${user.yearsOfExperience}
            Expected Salary: ${user.expectedSalary}

            SUMMARY:
            ${user.summary}

            SKILLS:
            ${user.skills.joinToString(", ")}

            EDUCATION:
            ${user.education.joinToString("\n") { "• ${it.degree} in ${it.fieldOfStudy} from ${it.institution} (${it.endDate})" }}

            EXPERIENCE:
            ${user.experience.joinToString("\n") { "• ${it.position} at ${it.company} (${it.startDate} - ${it.endDate}): ${it.description}" }}

            LANGUAGES:
            ${user.languages.joinToString(", ") { "${it.name} (${it.proficiency})" }}

            ACHIEVEMENTS:
            ${user.achievements}

            CERTIFICATES:
            ${user.certificates.joinToString("\n") { "• ${it["name"]} from ${it["issuer"]} (${it["date"]})" }}

            LINKS:
            LinkedIn: ${user.linkedin}
            GitHub: ${user.github}
            Portfolio: ${user.portfolio}

            REQUIREMENTS:
            1. Generate ONLY the HTML content (no explanations)
            2. Use modern, professional styling with CSS embedded in <style> tags
            3. Make it responsive and print-friendly
            4. Use appropriate colors for the ${user.field} field (tech = blue tones, finance = green tones, creative = purple tones, etc.)
            5. Include proper sections: Header, Summary, Skills, Experience, Education, Languages, Achievements, Certificates
            6. Use professional fonts like Arial, Helvetica, or similar
            7. Ensure good spacing and readability
            8. Make it ATS-friendly with clear section headers
            9. Include contact information prominently
            10. If any section is empty, skip it gracefully

            Generate the complete HTML document starting with <!DOCTYPE html>
        """.trimIndent()
    }

    private fun cleanHtmlContent(htmlContent: String): String {
        // Remove any markdown formatting that might have been added
        var cleaned = htmlContent
            .replace("```html", "")
            .replace("```", "")
            .trim()

        // Ensure it starts with DOCTYPE if not present
        if (!cleaned.startsWith("<!DOCTYPE")) {
            cleaned = "<!DOCTYPE html>\n$cleaned"
        }

        return cleaned
    }

    private fun generateFallbackCv(user: User): String {
        val fieldColor = getFieldColor(user.field)

        return """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${user.name} ${user.surname} - CV</title>
                <style>
                    body {
                        font-family: 'Arial', 'Helvetica', sans-serif;
                        line-height: 1.6;
                        margin: 0;
                        padding: 20px;
                        color: #333;
                        background-color: #fff;
                    }
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        box-shadow: 0 0 20px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    .header {
                        background: linear-gradient(135deg, $fieldColor, ${adjustColor(fieldColor, -20)});
                        color: white;
                        padding: 30px;
                        text-align: center;
                    }
                    .header h1 {
                        margin: 0;
                        font-size: 2.5em;
                        font-weight: 300;
                    }
                    .header p {
                        margin: 5px 0;
                        font-size: 1.1em;
                    }
                    .content {
                        padding: 30px;
                    }
                    .section {
                        margin-bottom: 30px;
                    }
                    .section h2 {
                        color: $fieldColor;
                        border-bottom: 2px solid $fieldColor;
                        padding-bottom: 5px;
                        margin-bottom: 15px;
                        font-size: 1.4em;
                    }
                    .skills {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 8px;
                    }
                    .skill {
                        background: ${adjustColor(fieldColor, 40)};
                        color: $fieldColor;
                        padding: 5px 12px;
                        border-radius: 20px;
                        font-size: 0.9em;
                        font-weight: 500;
                    }
                    .experience-item, .education-item {
                        margin-bottom: 20px;
                        padding-left: 20px;
                        border-left: 3px solid $fieldColor;
                    }
                    .item-title {
                        font-weight: bold;
                        color: $fieldColor;
                        font-size: 1.1em;
                    }
                    .item-company {
                        font-style: italic;
                        color: #666;
                    }
                    .item-date {
                        color: #888;
                        font-size: 0.9em;
                    }
                    @media print {
                        body { padding: 0; }
                        .container { box-shadow: none; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>${user.name} ${user.surname}</h1>
                        <p>${user.email} | ${user.phoneNumber}</p>
                        <p>${user.city}, ${user.province}</p>
                        ${if (user.linkedin.isNotEmpty() || user.github.isNotEmpty() || user.portfolio.isNotEmpty()) {
                            "<p>" + listOfNotNull(
                                if (user.linkedin.isNotEmpty()) "LinkedIn: ${user.linkedin}" else null,
                                if (user.github.isNotEmpty()) "GitHub: ${user.github}" else null,
                                if (user.portfolio.isNotEmpty()) "Portfolio: ${user.portfolio}" else null
                            ).joinToString(" | ") + "</p>"
                        } else ""}
                    </div>
                    <div class="content">
                        ${if (user.summary.isNotEmpty()) """
                            <div class="section">
                                <h2>Professional Summary</h2>
                                <p>${user.summary}</p>
                            </div>
                        """ else ""}

                        ${if (user.skills.isNotEmpty()) """
                            <div class="section">
                                <h2>Skills</h2>
                                <div class="skills">
                                    ${user.skills.joinToString("") { "<span class=\"skill\">$it</span>" }}
                                </div>
                            </div>
                        """ else ""}

                        ${if (user.experience.isNotEmpty()) """
                            <div class="section">
                                <h2>Professional Experience</h2>
                                ${user.experience.joinToString("") { exp ->
                                    """
                                    <div class="experience-item">
                                        <div class="item-title">${exp.position}</div>
                                        <div class="item-company">${exp.company}</div>
                                        <div class="item-date">${exp.startDate} - ${exp.endDate}</div>
                                        ${if (exp.description.isNotEmpty()) "<p>${exp.description}</p>" else ""}
                                    </div>
                                    """
                                }}
                            </div>
                        """ else ""}

                        ${if (user.education.isNotEmpty()) """
                            <div class="section">
                                <h2>Education</h2>
                                ${user.education.joinToString("") { edu ->
                                    """
                                    <div class="education-item">
                                        <div class="item-title">${edu.degree} in ${edu.fieldOfStudy}</div>
                                        <div class="item-company">${edu.institution}</div>
                                        <div class="item-date">${edu.endDate}</div>
                                    </div>
                                    """
                                }}
                            </div>
                        """ else ""}

                        ${if (user.languages.isNotEmpty()) """
                            <div class="section">
                                <h2>Languages</h2>
                                <p>${user.languages.joinToString(", ") { "${it.name} (${it.proficiency})" }}</p>
                            </div>
                        """ else ""}

                        ${if (user.achievements.isNotEmpty()) """
                            <div class="section">
                                <h2>Achievements</h2>
                                <p>${user.achievements}</p>
                            </div>
                        """ else ""}

                        ${if (user.certificates.isNotEmpty()) """
                            <div class="section">
                                <h2>Certifications</h2>
                                ${user.certificates.joinToString("") { cert ->
                                    "<p>• ${cert["name"]} from ${cert["issuer"]} (${cert["date"]})</p>"
                                }}
                            </div>
                        """ else ""}
                    </div>
                </div>
            </body>
            </html>
        """.trimIndent()
    }

    private fun getFieldColor(field: String): String {
        return when (field.lowercase()) {
            "information technology", "technology", "it" -> "#2563eb"
            "finance", "accounting", "banking" -> "#059669"
            "marketing", "creative", "design" -> "#7c3aed"
            "healthcare", "medical" -> "#dc2626"
            "education", "teaching" -> "#ea580c"
            "engineering" -> "#0891b2"
            "law", "legal" -> "#4338ca"
            "human resources", "hr" -> "#be185d"
            else -> "#374151"
        }
    }

    private fun adjustColor(color: String, adjustment: Int): String {
        // Simple color adjustment - in a real implementation, you'd use proper color manipulation
        return color // For now, return the same color
    }
}
