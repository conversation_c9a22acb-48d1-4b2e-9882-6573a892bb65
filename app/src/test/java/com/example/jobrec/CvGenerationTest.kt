package com.example.jobrec

import com.example.jobrec.utils.CvGenerationUtils
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for CV Generation functionality
 */
class CvGenerationTest {

    @Test
    fun testProfileValidation_withCompleteProfile() {
        val user = createCompleteUser()
        val missingFields = CvGenerationUtils.validateUserProfile(user)
        assertTrue("Complete profile should have no missing fields", missingFields.isEmpty())
    }

    @Test
    fun testProfileValidation_withIncompleteProfile() {
        val user = User(
            name = "<PERSON>",
            surname = "", // Missing surname
            email = "<EMAIL>",
            phoneNumber = "", // Missing phone
            city = "Durban",
            province = "KwaZulu-Natal"
        )
        val missingFields = CvGenerationUtils.validateUserProfile(user)
        assertTrue("Incomplete profile should have missing fields", missingFields.isNotEmpty())
        assertTrue("Should identify missing surname", missingFields.contains("Surname"))
        assertTrue("Should identify missing phone", missingFields.contains("Phone Number"))
    }

    @Test
    fun testMinimumProfileData_withSufficientData() {
        val user = createCompleteUser()
        assertTrue("Complete profile should have sufficient data",
                  CvGenerationUtils.hasMinimumProfileData(user))
    }

    @Test
    fun testMinimumProfileData_withInsufficientData() {
        val user = User(
            name = "John",
            surname = "Doe",
            email = "<EMAIL>",
            phoneNumber = "+27 82 123 4567",
            // Missing summary, skills, experience, and education
            summary = "",
            skills = emptyList(),
            experience = emptyList(),
            education = emptyList()
        )
        assertFalse("Profile without content should not have sufficient data",
                   CvGenerationUtils.hasMinimumProfileData(user))
    }

    @Test
    fun testSimpleCvGeneration() {
        val user = createCompleteUser()
        val cvContent = CvGenerationUtils.generateSimpleCvContent(user)

        // Verify essential information is included
        assertTrue("CV should contain user name", cvContent.contains("John Doe"))
        assertTrue("CV should contain email", cvContent.contains("<EMAIL>"))
        assertTrue("CV should contain phone", cvContent.contains("+27 82 123 4567"))
        assertTrue("CV should contain location", cvContent.contains("Durban, KwaZulu-Natal"))
        assertTrue("CV should contain summary section", cvContent.contains("PROFESSIONAL SUMMARY"))
        assertTrue("CV should contain skills section", cvContent.contains("SKILLS"))
        assertTrue("CV should contain experience section", cvContent.contains("PROFESSIONAL EXPERIENCE"))
        assertTrue("CV should contain education section", cvContent.contains("EDUCATION"))
    }

    @Test
    fun testSimpleCvGeneration_withMinimalData() {
        val user = User(
            name = "Jane",
            surname = "Smith",
            email = "<EMAIL>",
            phoneNumber = "+27 83 987 6543",
            city = "Cape Town",
            province = "Western Cape",
            summary = "Software developer with 3 years experience",
            skills = listOf("Java", "Kotlin", "Android"),
            experience = emptyList(),
            education = emptyList()
        )

        val cvContent = CvGenerationUtils.generateSimpleCvContent(user)

        // Verify essential information is included
        assertTrue("CV should contain user name", cvContent.contains("Jane Smith"))
        assertTrue("CV should contain summary", cvContent.contains("Software developer with 3 years experience"))
        assertTrue("CV should contain skills", cvContent.contains("Java, Kotlin, Android"))

        // Verify empty sections are handled gracefully
        assertFalse("CV should not contain empty experience section",
                   cvContent.contains("PROFESSIONAL EXPERIENCE"))
        assertFalse("CV should not contain empty education section",
                   cvContent.contains("EDUCATION"))
    }

    private fun createCompleteUser(): User {
        return User(
            name = "John",
            surname = "Doe",
            email = "<EMAIL>",
            phoneNumber = "+27 82 123 4567",
            city = "Durban",
            province = "KwaZulu-Natal",
            summary = "Experienced software developer with expertise in mobile app development",
            skills = listOf("Kotlin", "Java", "Android", "Firebase", "Git"),
            experience = listOf(
                Experience(
                    company = "Tech Solutions",
                    position = "Senior Android Developer",
                    startDate = "2022-01",
                    endDate = "Present",
                    description = "Lead mobile app development projects"
                ),
                Experience(
                    company = "StartupCorp",
                    position = "Junior Developer",
                    startDate = "2020-06",
                    endDate = "2021-12",
                    description = "Developed mobile applications using Kotlin"
                )
            ),
            education = listOf(
                Education(
                    institution = "University of KwaZulu-Natal",
                    degree = "Bachelor of Science",
                    fieldOfStudy = "Computer Science",
                    startDate = "2017",
                    endDate = "2020"
                )
            ),
            languages = listOf(
                Language(
                    name = "English",
                    proficiency = "Native"
                ),
                Language(
                    name = "Zulu",
                    proficiency = "Fluent"
                )
            ),
            achievements = "Dean's List 2019, Best Mobile App Award 2021",
            certificates = listOf(
                mapOf(
                    "name" to "Android Developer Certification",
                    "issuer" to "Google",
                    "date" to "2021"
                )
            ),
            linkedin = "https://linkedin.com/in/johndoe",
            github = "https://github.com/johndoe",
            portfolio = "https://johndoe.dev",
            field = "Information Technology",
            subField = "Mobile Development",
            yearsOfExperience = "3-5 years"
        )
    }
}
